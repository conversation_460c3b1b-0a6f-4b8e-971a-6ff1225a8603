syntax = "v1"

info (
	title:   "ppanel API"
	desc:    "API for ppanel"
	author:  "Tension"
	email:   "<EMAIL>"
	version: "0.0.1"
)

import (
	"apis/common.api"
	"apis/node/node.api"
	"apis/auth/auth.api"
	"apis/admin/system.api"
	"apis/admin/user.api"
	"apis/admin/auth.api"
	"apis/admin/server.api"
	"apis/admin/subscribe.api"
	"apis/admin/payment.api"
	"apis/admin/coupon.api"
	"apis/admin/order.api"
	"apis/admin/ticket.api"
	"apis/admin/announcement.api"
	"apis/admin/document.api"
	"apis/admin/tool.api"
	"apis/admin/console.api"
	"apis/admin/log.api"
	"apis/admin/ads.api"
	"apis/public/user.api"
	"apis/public/subscribe.api"
	"apis/public/order.api"
	"apis/public/announcement.api"
	"apis/public/ticket.api"
	"apis/public/payment.api"
	"apis/public/document.api"
	"apis/public/portal.api"
	"apis/app/auth.api"
	"apis/app/user.api"
	"apis/app/node.api"
	"apis/app/ws.api"
	"apis/app/order.api"
	"apis/app/announcement.api"
	"apis/app/payment.api"
	"apis/app/document.api"
	"apis/app/subscribe.api"
)

