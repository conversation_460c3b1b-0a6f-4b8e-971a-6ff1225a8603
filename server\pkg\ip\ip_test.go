package ip

import (
	"testing"
	"time"
)

func TestGetIPv4(t *testing.T) {
	t.<PERSON><PERSON>("skip TestGetIPv4")
	iPv4, err := GetIP("baidu.com")
	if err != nil {
		t.<PERSON><PERSON>(err)
	}

	t.Log(iPv4)
}

func TestGetRegionByIp(t *testing.T) {
	t.<PERSON>p("skip TestGetRegionByIp")
	ips, err := GetIP("**************")
	if err != nil {
		t.Fatal(err)
	}

	for _, ip := range ips {
		t.Log(ip)
		resp, err := GetRegionByIp(ip)
		if err != nil {
			t.Fatalf("ip: %s,err: %v", ip, err)
		}
		t.Logf("country: %s,City: %s,latitude:%s, longitude:%s", resp.Country, resp.City, resp.Latitude, resp.Longitude)
	}
	time.Sleep(3 * time.Second)
}
