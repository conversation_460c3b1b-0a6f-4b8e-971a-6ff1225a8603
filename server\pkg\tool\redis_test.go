package tool

import "testing"

func TestParseRedisURI(t *testing.T) {
	uri := "redis://localhost:6379"
	addr, password, database, err := ParseRedisURI(uri)
	if err != nil {
		t.<PERSON><PERSON>(err)
	}
	t.Log(addr, password, database)
}

func TestRedisPing(t *testing.T) {
	uri := "redis://localhost:6379"
	addr, password, database, err := ParseRedisURI(uri)
	if err != nil {
		t.<PERSON>al(err)
	}
	err = RedisPing(addr, password, database)
	if err != nil {
		t.<PERSON>al(err)
	}
}
