import { create } from 'zustand';
import { persist } from 'zustand/middleware';

export interface UserInfo {
  id: number;
  uuid: string;
  username: string;
  email: string;
  role: 'admin' | 'user';
  status: 'active' | 'inactive' | 'expired';
  traffic_limit: number;
  traffic_used: number;
  device_limit: number;
  expired_at?: string;
  created_at: string;
  updated_at: string;
}

interface AuthStore {
  // 状态
  isAuthenticated: boolean;
  userInfo: UserInfo | null;
  token: string | null;
  
  // 方法
  login: (token: string, userInfo: UserInfo) => void;
  logout: () => void;
  updateUserInfo: (userInfo: Partial<UserInfo>) => void;
  
  // 权限检查
  isAdmin: () => boolean;
  hasPermission: (permission: string) => boolean;
}

export const useAuthStore = create<AuthStore>()(
  persist(
    (set, get) => ({
      isAuthenticated: false,
      userInfo: null,
      token: null,
      
      login: (token: string, userInfo: UserInfo) => {
        set({
          isAuthenticated: true,
          token,
          userInfo,
        });
      },
      
      logout: () => {
        set({
          isAuthenticated: false,
          token: null,
          userInfo: null,
        });
      },
      
      updateUserInfo: (updates: Partial<UserInfo>) => {
        const currentUser = get().userInfo;
        if (currentUser) {
          set({
            userInfo: { ...currentUser, ...updates },
          });
        }
      },
      
      isAdmin: () => {
        const userInfo = get().userInfo;
        return userInfo?.role === 'admin';
      },
      
      hasPermission: (permission: string) => {
        const userInfo = get().userInfo;
        if (!userInfo) return false;
        
        // 管理员拥有所有权限
        if (userInfo.role === 'admin') return true;
        
        // 这里可以根据需要扩展权限系统
        return false;
      },
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        token: state.token,
        userInfo: state.userInfo,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
); 