import { QueryClient } from '@tanstack/react-query';

export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      // 5分钟缓存时间
      staleTime: 5 * 60 * 1000,
      // 失败重试
      retry: (failureCount, error: any) => {
        // 认证错误不重试
        if (error?.response?.status === 401 || error?.response?.status === 403) {
          return false;
        }
        // 其他错误最多重试2次
        return failureCount < 2;
      },
      // 重试延迟
      retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000),
      // 网络重连时重新获取数据
      refetchOnReconnect: true,
      // 窗口获得焦点时重新获取数据
      refetchOnWindowFocus: false,
    },
    mutations: {
      // 失败重试
      retry: false,
    },
  },
}); 