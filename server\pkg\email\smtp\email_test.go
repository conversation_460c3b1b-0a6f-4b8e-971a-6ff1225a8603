package smtp

import "testing"

func TestEmailSend(t *testing.T) {
	t.<PERSON>("Skip TestEmailSend")
	config := &Config{
		Host:     "smtp.mail.me.com",
		Port:     587,
		User:     "<EMAIL>",
		Pass:     "password",
		From:     "<EMAIL>",
		SSL:      true,
		SiteName: "",
	}
	address := []string{"<EMAIL>"}
	subject := "test"
	body := "test"
	email := NewClient(config)
	err := email.Send(address, subject, body)
	if err != nil {
		t.<PERSON><PERSON><PERSON>("send email error: %v", err)
	}
}
