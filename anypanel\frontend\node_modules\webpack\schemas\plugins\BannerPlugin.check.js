/*
 * This file was automatically generated.
 * DO NOT MODIFY BY HAND.
 * Run `yarn fix:special` to update
 */
"use strict";function n(t,{instancePath:e="",parentData:s,parentDataProperty:l,rootData:a=t}={}){let r=null,o=0;const u=o;let i=!1;const p=o;if(o===p)if(Array.isArray(t)){const n=t.length;for(let e=0;e<n;e++){let n=t[e];const s=o,l=o;let a=!1,u=null;const i=o,p=o;let f=!1;const h=o;if(!(n instanceof RegExp)){const n={params:{}};null===r?r=[n]:r.push(n),o++}var c=h===o;if(f=f||c,!f){const t=o;if(o===t)if("string"==typeof n){if(n.length<1){const n={params:{}};null===r?r=[n]:r.push(n),o++}}else{const n={params:{type:"string"}};null===r?r=[n]:r.push(n),o++}c=t===o,f=f||c}if(f)o=p,null!==r&&(p?r.length=p:r=null);else{const n={params:{}};null===r?r=[n]:r.push(n),o++}if(i===o&&(a=!0,u=0),a)o=l,null!==r&&(l?r.length=l:r=null);else{const n={params:{passingSchemas:u}};null===r?r=[n]:r.push(n),o++}if(s!==o)break}}else{const n={params:{type:"array"}};null===r?r=[n]:r.push(n),o++}var f=p===o;if(i=i||f,!i){const n=o,e=o;let s=!1;const l=o;if(!(t instanceof RegExp)){const n={params:{}};null===r?r=[n]:r.push(n),o++}var h=l===o;if(s=s||h,!s){const n=o;if(o===n)if("string"==typeof t){if(t.length<1){const n={params:{}};null===r?r=[n]:r.push(n),o++}}else{const n={params:{type:"string"}};null===r?r=[n]:r.push(n),o++}h=n===o,s=s||h}if(s)o=e,null!==r&&(e?r.length=e:r=null);else{const n={params:{}};null===r?r=[n]:r.push(n),o++}f=n===o,i=i||f}if(!i){const t={params:{}};return null===r?r=[t]:r.push(t),o++,n.errors=r,!1}return o=u,null!==r&&(u?r.length=u:r=null),n.errors=r,0===o}function t(e,{instancePath:s="",parentData:l,parentDataProperty:a,rootData:r=e}={}){let o=null,u=0;const i=u;let p=!1;const c=u;if(u===c)if("string"==typeof e){if(e.length<1){const n={params:{}};null===o?o=[n]:o.push(n),u++}}else{const n={params:{type:"string"}};null===o?o=[n]:o.push(n),u++}var f=c===u;if(p=p||f,!p){const t=u;if(u===t)if(e&&"object"==typeof e&&!Array.isArray(e)){let t;if(void 0===e.banner&&(t="banner")){const n={params:{missingProperty:t}};null===o?o=[n]:o.push(n),u++}else{const t=u;for(const n in e)if("banner"!==n&&"entryOnly"!==n&&"exclude"!==n&&"footer"!==n&&"include"!==n&&"raw"!==n&&"stage"!==n&&"test"!==n){const t={params:{additionalProperty:n}};null===o?o=[t]:o.push(t),u++;break}if(t===u){if(void 0!==e.banner){let n=e.banner;const t=u,s=u;let l=!1;const a=u;if("string"!=typeof n){const n={params:{type:"string"}};null===o?o=[n]:o.push(n),u++}var h=a===u;if(l=l||h,!l){const t=u;if(!(n instanceof Function)){const n={params:{}};null===o?o=[n]:o.push(n),u++}h=t===u,l=l||h}if(l)u=s,null!==o&&(s?o.length=s:o=null);else{const n={params:{}};null===o?o=[n]:o.push(n),u++}var m=t===u}else m=!0;if(m){if(void 0!==e.entryOnly){const n=u;if("boolean"!=typeof e.entryOnly){const n={params:{type:"boolean"}};null===o?o=[n]:o.push(n),u++}m=n===u}else m=!0;if(m){if(void 0!==e.exclude){const t=u,l=u;let a=!1,i=null;const p=u;if(n(e.exclude,{instancePath:s+"/exclude",parentData:e,parentDataProperty:"exclude",rootData:r})||(o=null===o?n.errors:o.concat(n.errors),u=o.length),p===u&&(a=!0,i=0),a)u=l,null!==o&&(l?o.length=l:o=null);else{const n={params:{passingSchemas:i}};null===o?o=[n]:o.push(n),u++}m=t===u}else m=!0;if(m){if(void 0!==e.footer){const n=u;if("boolean"!=typeof e.footer){const n={params:{type:"boolean"}};null===o?o=[n]:o.push(n),u++}m=n===u}else m=!0;if(m){if(void 0!==e.include){const t=u,l=u;let a=!1,i=null;const p=u;if(n(e.include,{instancePath:s+"/include",parentData:e,parentDataProperty:"include",rootData:r})||(o=null===o?n.errors:o.concat(n.errors),u=o.length),p===u&&(a=!0,i=0),a)u=l,null!==o&&(l?o.length=l:o=null);else{const n={params:{passingSchemas:i}};null===o?o=[n]:o.push(n),u++}m=t===u}else m=!0;if(m){if(void 0!==e.raw){const n=u;if("boolean"!=typeof e.raw){const n={params:{type:"boolean"}};null===o?o=[n]:o.push(n),u++}m=n===u}else m=!0;if(m){if(void 0!==e.stage){const n=u;if("number"!=typeof e.stage){const n={params:{type:"number"}};null===o?o=[n]:o.push(n),u++}m=n===u}else m=!0;if(m)if(void 0!==e.test){const t=u,l=u;let a=!1,i=null;const p=u;if(n(e.test,{instancePath:s+"/test",parentData:e,parentDataProperty:"test",rootData:r})||(o=null===o?n.errors:o.concat(n.errors),u=o.length),p===u&&(a=!0,i=0),a)u=l,null!==o&&(l?o.length=l:o=null);else{const n={params:{passingSchemas:i}};null===o?o=[n]:o.push(n),u++}m=t===u}else m=!0}}}}}}}}}else{const n={params:{type:"object"}};null===o?o=[n]:o.push(n),u++}if(f=t===u,p=p||f,!p){const n=u;if(!(e instanceof Function)){const n={params:{}};null===o?o=[n]:o.push(n),u++}f=n===u,p=p||f}}if(!p){const n={params:{}};return null===o?o=[n]:o.push(n),u++,t.errors=o,!1}return u=i,null!==o&&(i?o.length=i:o=null),t.errors=o,0===u}module.exports=t,module.exports.default=t;