import React, { useState } from 'react';
import { Outlet, useNavigate, useLocation } from 'react-router-dom';
import {
  ProLayout,
  MenuDataItem,
  PageContainer,
} from '@ant-design/pro-components';
import {
  LogoutOutlined,
  UserOutlined,
  QuestionCircleOutlined,
  BellOutlined,
} from '@ant-design/icons';
import { Dropdown, Avatar, Badge, Button, Space, message } from 'antd';
import type { MenuProps } from 'antd';
import { useAuthStore } from '../../stores/useAuthStore';
import { getAccessibleRoutes, RouteConfig } from '../../config/routes';
import { checkAccess } from '../Access';

// 转换路由配置为菜单数据
const transformRoutes = (routes: RouteConfig[]): MenuDataItem[] => {
  return routes
    .filter(route => !route.hideInMenu)
    .map(route => ({
      path: route.path,
      name: route.name,
      icon: route.icon,
      children: route.routes ? transformRoutes(route.routes) : undefined,
      access: route.access,
    }));
};

const BasicLayout: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { userInfo, logout } = useAuthStore();
  const [collapsed, setCollapsed] = useState(false);

  // 获取当前用户可访问的路由
  const accessibleRoutes = getAccessibleRoutes(userInfo?.role);
  const menuData = transformRoutes(accessibleRoutes);

  // 用户下拉菜单
  const userMenuItems: MenuProps['items'] = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: '个人设置',
      onClick: () => {
        const profilePath = userInfo?.role === 'admin' ? '/admin/profile' : '/user/profile';
        navigate(profilePath);
      },
    },
    {
      type: 'divider',
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登录',
      onClick: () => {
        logout();
        message.success('已安全退出');
        navigate('/login');
      },
    },
  ];

  // 获取页面标题
  const getPageTitle = () => {
    const pathSegments = location.pathname.split('/').filter(Boolean);
    if (pathSegments.length > 0) {
      if (pathSegments[0] === 'admin') {
        return 'AnyTLS Panel - 管理员面板';
      } else if (pathSegments[0] === 'user') {
        return 'AnyTLS Panel - 用户面板';
      }
    }
    return 'AnyTLS Panel';
  };

  // 菜单项访问控制
  const menuDataRender = (menuList: MenuDataItem[]): MenuDataItem[] => {
    return menuList.filter(item => {
      if (item.access && !checkAccess(item.access, userInfo?.role)) {
        return false;
      }
      if (item.children) {
        item.children = menuDataRender(item.children);
      }
      return true;
    });
  };

  return (
    <ProLayout
      title="AnyTLS Panel"
      logo={false}
      layout="mix"
      theme="light"
      collapsed={collapsed}
      onCollapse={setCollapsed}
      location={{
        pathname: location.pathname,
      }}
      route={{
        routes: menuData,
      }}
      menuDataRender={menuDataRender}
      menuItemRender={(item, dom) => (
        <div
          onClick={() => {
            if (item.path) {
              navigate(item.path);
            }
          }}
        >
          {dom}
        </div>
      )}
      breadcrumbRender={(routers = []) => [
        {
          path: '/',
          breadcrumbName: '首页',
        },
        ...routers,
      ]}
      itemRender={(route, _, routes, paths) => {
        const isLast = routes.indexOf(route) === routes.length - 1;
        return isLast ? (
          <span>{route.breadcrumbName}</span>
        ) : (
          <span
            style={{ cursor: 'pointer' }}
            onClick={() => navigate(paths.join('/'))}
          >
            {route.breadcrumbName}
          </span>
        );
      }}
      headerTitleRender={() => (
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <div style={{ fontSize: '18px', fontWeight: 'bold', color: '#1890ff' }}>
            AnyTLS Panel
          </div>
          {userInfo?.role === 'admin' && (
            <div style={{ 
              marginLeft: '12px',
              padding: '2px 8px',
              background: '#f0f0f0',
              borderRadius: '4px',
              fontSize: '12px',
              color: '#666'
            }}>
              管理员
            </div>
          )}
        </div>
      )}
      headerContentRender={() => (
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <Button
            type="text"
            icon={<QuestionCircleOutlined />}
            onClick={() => {
              window.open('https://github.com/your-repo/anytls-panel/wiki', '_blank');
            }}
          >
            帮助文档
          </Button>
        </div>
      )}
      actionsRender={() => [
        <Badge key="notification" count={0} size="small">
          <Button
            type="text"
            icon={<BellOutlined />}
            onClick={() => {
              message.info('暂无新消息');
            }}
          />
        </Badge>,
        <Dropdown
          key="user"
          menu={{ items: userMenuItems }}
          placement="bottomRight"
        >
          <Space style={{ cursor: 'pointer', padding: '0 8px' }}>
            <Avatar size="small" icon={<UserOutlined />} />
            <span>{userInfo?.username || '用户'}</span>
          </Space>
        </Dropdown>,
      ]}
      menuProps={{
        style: { border: 'none' },
      }}
      contentStyle={{
        margin: 0,
        padding: 0,
      }}
    >
      <PageContainer
        header={{
          title: getPageTitle(),
          ghost: true,
        }}
        content={
          <div style={{ minHeight: 'calc(100vh - 112px)' }}>
            <Outlet />
          </div>
        }
      />
    </ProLayout>
  );
};

export default BasicLayout; 