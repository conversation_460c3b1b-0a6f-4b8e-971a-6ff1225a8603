package cache

const (
	// UserTodayUploadTrafficCacheKey 用户当日上传流量
	UserTodayUploadTrafficCacheKey = "node:user_today_upload_traffic"
	// UserTodayDownloadTrafficCacheKey 用户当日下载流量
	UserTodayDownloadTrafficCacheKey = "node:user_today_download_traffic"
	// UserTodayTotalTrafficCacheKey 用户当日总流量
	UserTodayTotalTrafficCacheKey = "node:user_today_total_traffic"
	// NodeTodayUploadTrafficCacheKey 节点当日上传流量
	NodeTodayUploadTrafficCacheKey = "node:node_today_upload_traffic"
	// NodeTodayDownloadTrafficCacheKey 节点当日下载流量
	NodeTodayDownloadTrafficCacheKey = "node:node_today_download_traffic"
	// NodeTodayTotalTrafficCacheKey 节点当日总流量
	NodeTodayTotalTrafficCacheKey = "node:node_today_total_traffic"
	// UserTodayUploadTrafficRankKey 用户当日上传流量排行榜
	UserTodayUploadTrafficRankKey = "node:user_today_upload_traffic_rank"
	// UserTodayDownloadTrafficRankKey 用户当日下载流量排行榜
	UserTodayDownloadTrafficRankKey = "node:user_today_download_traffic_rank"
	// UserTodayTotalTrafficRankKey 用户当日总流量排行榜
	UserTodayTotalTrafficRankKey = "node:user_today_total_traffic_rank"
	// NodeTodayUploadTrafficRankKey 节点当日上传流量排行榜
	NodeTodayUploadTrafficRankKey = "node:node_today_upload_traffic_rank"
	// NodeTodayDownloadTrafficRankKey 节点当日下载流量排行榜
	NodeTodayDownloadTrafficRankKey = "node:node_today_download_traffic_rank"
	// NodeTodayTotalTrafficRankKey 节点当日总流量排行榜
	NodeTodayTotalTrafficRankKey = "node:node_today_total_traffic_rank"
	// NodeOnlineUserCacheKey 节点在线用户
	NodeOnlineUserCacheKey = "node:node_online_user:%d"
	// UserOnlineIpCacheKey 用户在线IP
	UserOnlineIpCacheKey = "node:user_online_ip:%d"
	// AllNodeOnlineUserCacheKey 所有节点在线用户
	AllNodeOnlineUserCacheKey = "node:all_node_online_user"
	// NodeStatusCacheKey 节点状态
	NodeStatusCacheKey = "node:status:%d"
	// AllNodeDownloadTrafficCacheKey 所有节点下载流量
	AllNodeDownloadTrafficCacheKey = "node:all_node_download_traffic"
	// AllNodeUploadTrafficCacheKey 所有节点上传流量
	AllNodeUploadTrafficCacheKey = "node:all_node_upload_traffic"
	// YesterdayTotalTrafficRank 昨日节点总流量排行榜
	YesterdayNodeTotalTrafficRank = "node:yesterday_total_traffic_rank"
	// YesterdayUploadTrafficRank 昨日节点上传流量排行榜
	YesterdayNodeUploadTrafficRank = "node:yesterday_upload_traffic_rank"
	// YesterdayDownloadTrafficRank 昨日节点下载流量排行榜
	YesterdayNodeDownloadTrafficRank = "node:yesterday_download_traffic_rank"
	// YesterdayUserTotalTrafficRank 昨日用户总流量排行榜
	YesterdayUserTotalTrafficRank = "node:yesterday_user_total_traffic_rank"
	// YesterdayUserUploadTrafficRank 昨日用户上传流量排行榜
	YesterdayUserUploadTrafficRank = "node:yesterday_user_upload_traffic_rank"
	// YesterdayUserDownloadTrafficRank 昨日用户下载流量排行榜
	YesterdayUserDownloadTrafficRank = "node:yesterday_user_download_traffic_rank"
)
