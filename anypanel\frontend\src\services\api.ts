import axios, { AxiosInstance, AxiosResponse, AxiosError } from 'axios';
import { message } from 'antd';
import { useAuthStore } from '../stores/useAuthStore';

// API响应类型
export interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data: T;
  code?: number;
}

// 分页参数类型
export interface PaginationParams {
  current?: number;
  pageSize?: number;
  [key: string]: any;
}

// 分页响应类型
export interface PaginatedResponse<T> {
  list: T[];
  total: number;
  current: number;
  pageSize: number;
}

class ApiClient {
  private instance: AxiosInstance;

  constructor() {
    this.instance = axios.create({
      baseURL: '/api',
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    this.setupInterceptors();
  }

  private setupInterceptors() {
    // 请求拦截器：添加认证token
    this.instance.interceptors.request.use(
      (config) => {
        const token = useAuthStore.getState().token;
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // 响应拦截器：统一错误处理
    this.instance.interceptors.response.use(
      (response: AxiosResponse<ApiResponse>) => {
        const data = response.data;
        
        // 如果响应数据不符合标准格式，直接返回
        if (typeof data !== 'object' || data === null) {
          return response;
        }

        // 处理业务错误
        if (data.success === false) {
          message.error(data.message || '请求失败');
          return Promise.reject(new Error(data.message || '请求失败'));
        }

        return response;
      },
      (error: AxiosError<ApiResponse>) => {
        const { response } = error;
        
        if (response?.status === 401) {
          // 处理认证失败
          message.error('登录已过期，请重新登录');
          useAuthStore.getState().logout();
          window.location.href = '/login';
        } else if (response?.status === 403) {
          message.error('权限不足');
        } else if (response?.status === 404) {
          message.error('请求的资源不存在');
        } else if (response?.status === 500) {
          message.error('服务器内部错误');
        } else if (error.code === 'NETWORK_ERROR') {
          message.error('网络连接失败');
        } else {
          const errorMessage = response?.data?.message || error.message || '请求失败';
          message.error(errorMessage);
        }

        return Promise.reject(error);
      }
    );
  }

  // GET请求
  async get<T = any>(url: string, params?: any): Promise<T> {
    const response = await this.instance.get<ApiResponse<T>>(url, { params });
    return response.data.data;
  }

  // POST请求
  async post<T = any>(url: string, data?: any): Promise<T> {
    const response = await this.instance.post<ApiResponse<T>>(url, data);
    return response.data.data;
  }

  // PUT请求
  async put<T = any>(url: string, data?: any): Promise<T> {
    const response = await this.instance.put<ApiResponse<T>>(url, data);
    return response.data.data;
  }

  // DELETE请求
  async delete<T = any>(url: string): Promise<T> {
    const response = await this.instance.delete<ApiResponse<T>>(url);
    return response.data.data;
  }

  // 上传文件
  async upload<T = any>(url: string, formData: FormData): Promise<T> {
    const response = await this.instance.post<ApiResponse<T>>(url, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data.data;
  }

  // 下载文件
  async download(url: string, params?: any): Promise<Blob> {
    const response = await this.instance.get(url, {
      params,
      responseType: 'blob',
    });
    return response.data;
  }

  // 获取原始axios实例（用于特殊需求）
  getInstance(): AxiosInstance {
    return this.instance;
  }
}

// 导出单例
export const apiClient = new ApiClient();
export default apiClient; 