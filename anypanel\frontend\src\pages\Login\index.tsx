import React, { useState } from 'react';
import { Form, Input, Button, Card, Typography, message, Divider } from 'antd';
import { UserOutlined, LockOutlined, SafetyOutlined } from '@ant-design/icons';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { useAuthStore } from '../../stores/useAuthStore';
import type { UserInfo } from '../../stores/useAuthStore';

const { Title, Text } = Typography;

interface LoginForm {
  username: string;
  password: string;
}

const Login: React.FC = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { login } = useAuthStore();

  const handleLogin = async (values: LoginForm) => {
    setLoading(true);
    try {
      // 模拟登录API调用
      // 在实际项目中，这里应该调用真实的登录API
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // 模拟登录响应
      const mockToken = 'mock-jwt-token';
      const mockUserInfo: UserInfo = {
        id: 1,
        uuid: 'mock-uuid-123',
        username: values.username,
        email: `${values.username}@example.com`,
        role: values.username === 'admin' ? 'admin' : 'user',
        status: 'active',
        traffic_limit: 1073741824000, // 1TB
        traffic_used: 107374182400, // 100GB
        device_limit: 3,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };

      // 更新认证状态
      login(mockToken, mockUserInfo);
      
      message.success('登录成功！');
      
      // 获取重定向路径或根据角色跳转
      const redirectPath = searchParams.get('redirect') || 
        (mockUserInfo.role === 'admin' ? '/admin/dashboard' : '/user/dashboard');
      
      navigate(redirectPath, { replace: true });
    } catch (error) {
      message.error('登录失败，请检查用户名和密码');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div style={{
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      padding: '20px'
    }}>
      <Card
        style={{
          width: '100%',
          maxWidth: '400px',
          boxShadow: '0 4px 20px rgba(0, 0, 0, 0.15)',
          borderRadius: '12px'
        }}
      >
        <div style={{ textAlign: 'center', marginBottom: '32px' }}>
          <SafetyOutlined 
            style={{ 
              fontSize: '48px', 
              color: '#1890ff',
              marginBottom: '16px'
            }} 
          />
          <Title level={2} style={{ margin: 0, color: '#1890ff' }}>
            AnyTLS Panel
          </Title>
          <Text type="secondary">
            管理面板登录
          </Text>
        </div>

        <Form
          form={form}
          name="login"
          onFinish={handleLogin}
          layout="vertical"
          size="large"
        >
          <Form.Item
            name="username"
            label="用户名"
            rules={[
              { required: true, message: '请输入用户名' },
              { min: 3, message: '用户名至少3个字符' }
            ]}
          >
            <Input 
              prefix={<UserOutlined />} 
              placeholder="请输入用户名"
              autoComplete="username"
            />
          </Form.Item>

          <Form.Item
            name="password"
            label="密码"
            rules={[
              { required: true, message: '请输入密码' },
              { min: 6, message: '密码至少6个字符' }
            ]}
          >
            <Input.Password 
              prefix={<LockOutlined />} 
              placeholder="请输入密码"
              autoComplete="current-password"
            />
          </Form.Item>

          <Form.Item style={{ marginBottom: '16px' }}>
            <Button 
              type="primary" 
              htmlType="submit" 
              block 
              loading={loading}
              style={{ height: '44px', fontSize: '16px' }}
            >
              {loading ? '登录中...' : '登录'}
            </Button>
          </Form.Item>
        </Form>

        <Divider>演示账号</Divider>

        <div style={{ 
          background: '#f8f9fa', 
          padding: '16px', 
          borderRadius: '8px',
          fontSize: '14px'
        }}>
          <div style={{ marginBottom: '8px' }}>
            <Text strong>管理员账号：</Text>
            <Text code>admin</Text> / <Text code>123456</Text>
          </div>
          <div>
            <Text strong>普通用户：</Text>
            <Text code>user</Text> / <Text code>123456</Text>
          </div>
        </div>

        <div style={{ 
          textAlign: 'center', 
          marginTop: '24px',
          color: '#8c8c8c',
          fontSize: '12px'
        }}>
          © 2024 AnyTLS Panel. All rights reserved.
        </div>
      </Card>
    </div>
  );
};

export default Login; 