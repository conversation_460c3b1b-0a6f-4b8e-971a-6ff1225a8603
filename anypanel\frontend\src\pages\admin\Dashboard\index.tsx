import React from 'react';
import { Row, Col, Card, Statistic, Typography } from 'antd';
import { ProCard } from '@ant-design/pro-components';
import {
  UserOutlined,
  NodeIndexOutlined,
  CloudServerOutlined,
  DollarOutlined,
  TrophyOutlined,
  RiseOutlined,
} from '@ant-design/icons';

const { Title } = Typography;

// 模拟数据（后续会替换为真实API数据）
const mockStats = {
  totalUsers: 1248,
  activeUsers: 892,
  totalNodes: 12,
  onlineNodes: 10,
  todayRevenue: 15642.50,
  monthlyRevenue: 487230.80,
  todayTraffic: 2.34, // TB
  monthlyTraffic: 89.7, // TB
};

const AdminDashboard: React.FC = () => {
  return (
    <div style={{ padding: '24px' }}>
      <Title level={2} style={{ marginBottom: '24px' }}>
        数据概览
      </Title>

      {/* 统计卡片行 */}
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        <Col xs={24} sm={12} md={6}>
          <ProCard>
            <Statistic
              title="总用户数"
              value={mockStats.totalUsers}
              prefix={<UserOutlined />}
              valueStyle={{ color: '#3f8600' }}
            />
          </ProCard>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <ProCard>
            <Statistic
              title="活跃用户"
              value={mockStats.activeUsers}
              prefix={<CloudServerOutlined />}
              suffix={`/ ${mockStats.totalUsers}`}
              valueStyle={{ color: '#1890ff' }}
            />
          </ProCard>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <ProCard>
            <Statistic
              title="在线节点"
              value={mockStats.onlineNodes}
              prefix={<NodeIndexOutlined />}
              suffix={`/ ${mockStats.totalNodes}`}
              valueStyle={{ color: '#722ed1' }}
            />
          </ProCard>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <ProCard>
            <Statistic
              title="今日收入"
              value={mockStats.todayRevenue}
              prefix={<DollarOutlined />}
              precision={2}
              suffix="¥"
              valueStyle={{ color: '#cf1322' }}
            />
          </ProCard>
        </Col>
      </Row>

      {/* 详细统计行 */}
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        <Col xs={24} sm={12} md={8}>
          <ProCard title="收入统计" headerBordered>
            <Row gutter={16}>
              <Col span={12}>
                <Statistic
                  title="本月收入"
                  value={mockStats.monthlyRevenue}
                  prefix={<TrophyOutlined />}
                  precision={2}
                  suffix="¥"
                  valueStyle={{ color: '#52c41a', fontSize: '20px' }}
                />
              </Col>
              <Col span={12}>
                <Statistic
                  title="增长率"
                  value={12.5}
                  prefix={<RiseOutlined />}
                  suffix="%"
                  valueStyle={{ color: '#3f8600', fontSize: '20px' }}
                />
              </Col>
            </Row>
          </ProCard>
        </Col>
        <Col xs={24} sm={12} md={8}>
          <ProCard title="流量统计" headerBordered>
            <Row gutter={16}>
              <Col span={12}>
                <Statistic
                  title="今日流量"
                  value={mockStats.todayTraffic}
                  precision={2}
                  suffix="TB"
                  valueStyle={{ color: '#1890ff', fontSize: '20px' }}
                />
              </Col>
              <Col span={12}>
                <Statistic
                  title="本月流量"
                  value={mockStats.monthlyTraffic}
                  precision={1}
                  suffix="TB"
                  valueStyle={{ color: '#722ed1', fontSize: '20px' }}
                />
              </Col>
            </Row>
          </ProCard>
        </Col>
        <Col xs={24} sm={24} md={8}>
          <ProCard title="系统状态" headerBordered>
            <Row gutter={16}>
              <Col span={12}>
                <Statistic
                  title="服务运行时间"
                  value="15天12小时"
                  valueStyle={{ color: '#52c41a', fontSize: '16px' }}
                />
              </Col>
              <Col span={12}>
                <Statistic
                  title="系统负载"
                  value="正常"
                  valueStyle={{ color: '#52c41a', fontSize: '16px' }}
                />
              </Col>
            </Row>
          </ProCard>
        </Col>
      </Row>

      {/* 快捷操作 */}
      <Row gutter={[16, 16]}>
        <Col span={24}>
          <ProCard title="快捷操作" headerBordered>
            <Row gutter={16}>
              <Col xs={24} sm={12} md={6}>
                <Card
                  hoverable
                  style={{ textAlign: 'center', cursor: 'pointer' }}
                  onClick={() => window.open('/admin/users', '_self')}
                >
                  <UserOutlined style={{ fontSize: '32px', color: '#1890ff', marginBottom: '8px' }} />
                  <div>用户管理</div>
                </Card>
              </Col>
              <Col xs={24} sm={12} md={6}>
                <Card
                  hoverable
                  style={{ textAlign: 'center', cursor: 'pointer' }}
                  onClick={() => window.open('/admin/nodes', '_self')}
                >
                  <NodeIndexOutlined style={{ fontSize: '32px', color: '#52c41a', marginBottom: '8px' }} />
                  <div>节点管理</div>
                </Card>
              </Col>
              <Col xs={24} sm={12} md={6}>
                <Card
                  hoverable
                  style={{ textAlign: 'center', cursor: 'pointer' }}
                  onClick={() => window.open('/admin/products', '_self')}
                >
                  <TrophyOutlined style={{ fontSize: '32px', color: '#722ed1', marginBottom: '8px' }} />
                  <div>商品管理</div>
                </Card>
              </Col>
              <Col xs={24} sm={12} md={6}>
                <Card
                  hoverable
                  style={{ textAlign: 'center', cursor: 'pointer' }}
                  onClick={() => window.open('/admin/traffic', '_self')}
                >
                  <RiseOutlined style={{ fontSize: '32px', color: '#fa8c16', marginBottom: '8px' }} />
                  <div>流量分析</div>
                </Card>
              </Col>
            </Row>
          </ProCard>
        </Col>
      </Row>
    </div>
  );
};

export default AdminDashboard; 