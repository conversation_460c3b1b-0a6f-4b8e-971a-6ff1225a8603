import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { Result, Button } from 'antd';
import { useAuthStore } from '../../stores/useAuthStore';

interface AccessProps {
  accessible?: boolean;
  fallback?: React.ReactNode;
  children: React.ReactNode;
}

// 权限控制组件
export const Access: React.FC<AccessProps> = ({
  accessible = true,
  fallback,
  children,
}) => {
  if (!accessible) {
    return fallback ? (
      <>{fallback}</>
    ) : (
      <Result
        status="403"
        title="403"
        subTitle="抱歉，您没有权限访问此页面。"
        extra={
          <Button type="primary" onClick={() => window.history.back()}>
            返回上一页
          </Button>
        }
      />
    );
  }

  return <>{children}</>;
};

// 路由权限守卫
interface RouteGuardProps {
  children: React.ReactNode;
  access?: string;
  requireAuth?: boolean;
}

export const RouteGuard: React.FC<RouteGuardProps> = ({
  children,
  access,
  requireAuth = true,
}) => {
  const location = useLocation();
  const { isAuthenticated, userInfo } = useAuthStore();

  // 如果需要认证但用户未登录，重定向到登录页
  if (requireAuth && !isAuthenticated) {
    return (
      <Navigate 
        to={`/login?redirect=${encodeURIComponent(location.pathname)}`} 
        replace 
      />
    );
  }

  // 如果已登录但访问登录页，重定向到对应的面板
  if (isAuthenticated && (location.pathname === '/login' || location.pathname === '/')) {
    const redirectPath = userInfo?.role === 'admin' ? '/admin/dashboard' : '/user/dashboard';
    return <Navigate to={redirectPath} replace />;
  }

  // 检查特定权限
  if (access && userInfo) {
    const hasAccess = checkAccess(access, userInfo.role);
    
    if (!hasAccess) {
      return (
        <Result
          status="403"
          title="403"
          subTitle="抱歉，您没有权限访问此页面。"
          extra={
            <Button type="primary" onClick={() => window.history.back()}>
              返回上一页
            </Button>
          }
        />
      );
    }
  }

  return <>{children}</>;
};

// 权限检查函数
export const checkAccess = (access: string, userRole?: string): boolean => {
  if (!userRole) return false;

  // 管理员拥有所有权限
  if (userRole === 'admin') return true;

  // 检查具体权限
  switch (access) {
    case 'admin':
      return userRole === 'admin';
    case 'user':
      return userRole === 'user' || userRole === 'admin';
    default:
      return false;
  }
};

// 权限检查Hook
export const useAccess = () => {
  const { userInfo } = useAuthStore();

  return {
    canAccessAdmin: checkAccess('admin', userInfo?.role),
    canAccessUser: checkAccess('user', userInfo?.role),
    checkAccess: (access: string) => checkAccess(access, userInfo?.role),
  };
}; 