package singbox

const DefaultTemplate = `
{
      "log": {
    "level": "info",
    "timestamp": true
  },
  "experimental": {
    "clash_api": {
      "external_controller": "127.0.0.1:9090",
      "external_ui": "ui",
      "secret": "",
      "external_ui_download_url": "https://mirror.ghproxy.com/https://github.com/MetaCubeX/Yacd-meta/archive/gh-pages.zip",
      "external_ui_download_detour": "direct",
      "default_mode": "rule"
    },
    "cache_file": {
      "enabled": true,
      "store_fakeip": false
    }
  },
  "dns": {
    "servers": [
      {
        "tag": "dns_proxy",
        "address": "tls://*******",
        "detour": "手动选择"
      },
      {
        "tag": "dns_direct",
        "address": "https://*********/dns-query",
        "detour": "DIRECT"
      }
    ],
    "rules": [
      {
        "outbound": "any",
        "server": "dns_direct",
        "disable_cache": true
      },
      {
        "rule_set": "geosite-cn",
        "server": "dns_direct"
      },
      {
        "clash_mode": "direct",
        "server": "dns_direct"
      },
      {
        "clash_mode": "global",
        "server": "dns_proxy"
      },
      {
        "rule_set": "geosite-geolocation-!cn",
        "server": "dns_proxy"
      }
    ],
    "final": "dns_direct",
    "strategy": "ipv4_only"
  },
  "route": {
    "rules": [
      {
        "action": "sniff"
      },
      {
        "protocol": "dns",
        "action": "hijack-dns"
      }
    ]
  },
  "inbounds": [
    {
      "tag": "tun-in",
      "type": "tun",
      "address": [
        "**********/30",
        "fdfe:dcba:9876::1/126"
      ],
      "auto_route": true,
      "strict_route": true,
      "stack": "system",
      "platform": {
        "http_proxy": {
          "enabled": true,
          "server": "127.0.0.1",
          "server_port": 7890
        }
      }
    },
    {
      "tag": "mixed-in",
      "type": "mixed",
      "listen": "127.0.0.1",
      "listen_port": 7890
    }
  ]
}
`
