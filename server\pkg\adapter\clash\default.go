package clash

const DefaultTemplate = `
mode: rule
ipv6: true
allow-lan: true
bind-address: "*"
mixed-port: 7890
log-level: error
unified-delay: true
tcp-concurrent: true
external-controller: 0.0.0.0:9090

tun:
  enable: true
  stack: system
  auto-route: true

dns:
  enable: true
  cache-algorithm: arc
  listen: 0.0.0.0:1053
  ipv6: true
  enhanced-mode: fake-ip
  fake-ip-range: **********/16
  fake-ip-filter:
    - "*.lan"
    - "lens.l.google.com"
    - "*.srv.nintendo.net"
    - "*.stun.playstation.net"
    - "xbox.*.*.microsoft.com"
    - "*.xboxlive.com"
    - "*.msftncsi.com"
    - "*.msftconnecttest.com"
  default-nameserver:
    - ************
    - *********
  nameserver:
    - system
    - ************
    - *********
  fallback:
    - *******
    - *******
  fallback-filter:
    geoip: true
    geoip-code: CN

proxies:

proxy-groups:

rules:
`
