package syncx

import (
	"sync"

	"github.com/perfect-panel/server/pkg/lang"
)

// A Done<PERSON>han is used as a channel that can be closed multiple times and wait for done.
type <PERSON><PERSON><PERSON> struct {
	done chan lang.PlaceholderType
	once sync.Once
}

// NewDoneChan returns a DoneChan.
func NewDoneChan() *<PERSON><PERSON><PERSON> {
	return &<PERSON><PERSON>han{
		done: make(chan lang.PlaceholderType),
	}
}

// Close closes dc, it's safe to close more than once.
func (dc *<PERSON><PERSON><PERSON>) Close() {
	dc.once.Do(func() {
		close(dc.done)
	})
}

// Done returns a channel that can be notified on dc closed.
func (dc *<PERSON><PERSON><PERSON>) Done() chan lang.PlaceholderType {
	return dc.done
}
