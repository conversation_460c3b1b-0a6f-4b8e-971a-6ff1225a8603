import {
  DashboardOutlined,
  UserOutlined,
  NodeIndexOutlined,
  TeamOutlined,
  ShoppingCartOutlined,
  FileTextOutlined,
  BarChartOutlined,
  SettingOutlined,
  SafetyOutlined,
  ShopOutlined,
  CrownOutlined,
  HomeOutlined,
} from '@ant-design/icons';

export interface RouteConfig {
  path: string;
  name: string;
  icon?: React.ReactNode;
  component?: React.LazyExoticComponent<React.ComponentType<any>>;
  hideInMenu?: boolean;
  access?: string;
  routes?: RouteConfig[];
}

// 管理员路由配置
export const adminRoutes: RouteConfig[] = [
  {
    path: '/admin',
    name: '管理员面板',
    icon: <CrownOutlined />,
    access: 'admin',
    routes: [
      {
        path: '/admin/dashboard',
        name: '仪表板',
        icon: <DashboardOutlined />,
        access: 'admin',
      },
      {
        path: '/admin/users',
        name: '用户管理',
        icon: <UserOutlined />,
        access: 'admin',
      },
      {
        path: '/admin/nodes',
        name: '节点管理',
        icon: <NodeIndexOutlined />,
        access: 'admin',
      },
      {
        path: '/admin/permission-groups',
        name: '权限组管理',
        icon: <TeamOutlined />,
        access: 'admin',
      },
      {
        path: '/admin/products',
        name: '商品管理',
        icon: <ShopOutlined />,
        access: 'admin',
      },
      {
        path: '/admin/orders',
        name: '订单管理',
        icon: <ShoppingCartOutlined />,
        access: 'admin',
      },
      {
        path: '/admin/traffic',
        name: '流量分析',
        icon: <BarChartOutlined />,
        access: 'admin',
      },
      {
        path: '/admin/reports',
        name: '报表中心',
        icon: <FileTextOutlined />,
        access: 'admin',
      },
      {
        path: '/admin/settings',
        name: '系统设置',
        icon: <SettingOutlined />,
        access: 'admin',
      },
    ],
  },
];

// 用户路由配置
export const userRoutes: RouteConfig[] = [
  {
    path: '/user',
    name: '用户面板',
    icon: <HomeOutlined />,
    access: 'user',
    routes: [
      {
        path: '/user/dashboard',
        name: '用户仪表板',
        icon: <DashboardOutlined />,
        access: 'user',
      },
      {
        path: '/user/store',
        name: '商品商店',
        icon: <ShopOutlined />,
        access: 'user',
      },
      {
        path: '/user/subscription',
        name: '订阅中心',
        icon: <SafetyOutlined />,
        access: 'user',
      },
      {
        path: '/user/profile',
        name: '个人设置',
        icon: <UserOutlined />,
        access: 'user',
      },
    ],
  },
];

// 公共路由配置
export const publicRoutes: RouteConfig[] = [
  {
    path: '/login',
    name: '登录',
    hideInMenu: true,
  },
  {
    path: '/register',
    name: '注册',
    hideInMenu: true,
  },
  {
    path: '/forgot-password',
    name: '忘记密码',
    hideInMenu: true,
  },
];

// 所有路由配置
export const allRoutes: RouteConfig[] = [
  ...publicRoutes,
  ...adminRoutes,
  ...userRoutes,
];

// 根据用户角色获取可访问的路由
export const getAccessibleRoutes = (userRole?: string): RouteConfig[] => {
  if (!userRole) {
    return publicRoutes;
  }

  const routes = [...publicRoutes];

  if (userRole === 'admin') {
    routes.push(...adminRoutes);
  } else if (userRole === 'user') {
    routes.push(...userRoutes);
  }

  return routes;
};

// 扁平化路由配置（用于路由注册）
export const flattenRoutes = (routes: RouteConfig[]): RouteConfig[] => {
  const result: RouteConfig[] = [];
  
  const flatten = (routeList: RouteConfig[]) => {
    routeList.forEach(route => {
      result.push(route);
      if (route.routes) {
        flatten(route.routes);
      }
    });
  };
  
  flatten(routes);
  return result;
}; 