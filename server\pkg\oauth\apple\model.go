package apple

// WebValidationTokenRequest is based off of https://developer.apple.com/documentation/signinwithapplerestapi/generate_and_validate_tokens
type WebValidationTokenRequest struct {
	// ClientID is the "Services ID" value that you get when navigating to your "sign in with Apple"-enabled service ID
	ClientID string

	// ClientSecret is secret generated as a JSON Web Token that uses the secret key generated by the WWDR portal.
	// It can also be generated using the GenerateClientSecret function provided in this package
	ClientSecret string

	// Code is the authorization code received from your application’s user agent.
	// The code is single use only and valid for five minutes.
	Code string

	// RedirectURI is the destination URI the code was originally sent to.
	// Redirect URLs must be registered with Apple. You can register up to 10. Apple will throw an error with IP address
	// URLs on the authorization screen, and will not let you add localhost in the developer portal.
	RedirectURI string
}

// CallbackRequest Apple Callback Request
type CallbackRequest struct {
	// Code is the authorization code received from your application’s user agent.
	// The code is single use only and valid for five minutes.
	Code    string `form:"code"`
	IdToken string `form:"id_token"`
	State   string `form:"state"`
}

// AppValidationTokenRequest is based off of https://developer.apple.com/documentation/signinwithapplerestapi/generate_and_validate_tokens
type AppValidationTokenRequest struct {
	// ClientID is the package name of your app
	ClientID string

	// ClientSecret is secret generated as a JSON Web Token that uses the secret key generated by the WWDR portal.
	// It can also be generated using the GenerateClientSecret function provided in this package
	ClientSecret string

	// The authorization code received in an authorization response sent to your app. The code is single-use only and valid for five minutes.
	// Authorization code validation requests require this parameter.
	Code string
}

// ValidationRefreshRequest is based off of https://developer.apple.com/documentation/signinwithapplerestapi/generate_and_validate_tokens
type ValidationRefreshRequest struct {
	// ClientID is the "Services ID" value that you get when navigating to your "sign in with Apple"-enabled service ID
	ClientID string

	// ClientSecret is secret generated as a JSON Web Token that uses the secret key generated by the WWDR portal.
	// It can also be generated using the GenerateClientSecret function provided in this package
	ClientSecret string

	// RefreshToken is the refresh token given during a previous validation
	RefreshToken string
}

// RevokeAccessTokenRequest is based off https://developer.apple.com/documentation/sign_in_with_apple/revoke_tokens
type RevokeAccessTokenRequest struct {
	// ClientID is the "Services ID" value that you get when navigating to your "sign in with Apple"-enabled service ID
	ClientID string

	// ClientSecret is secret generated as a JSON Web Token that uses the secret key generated by the WWDR portal.
	// It can also be generated using the GenerateClientSecret function provided in this package
	ClientSecret string

	// AccessToken is the auth token given during a previous validation
	AccessToken string
}

// RevokeRefreshTokenRequest is based off https://developer.apple.com/documentation/sign_in_with_apple/revoke_tokens
type RevokeRefreshTokenRequest struct {
	// ClientID is the "Services ID" value that you get when navigating to your "sign in with Apple"-enabled service ID
	ClientID string

	// ClientSecret is secret generated as a JSON Web Token that uses the secret key generated by the WWDR portal.
	// It can also be generated using the GenerateClientSecret function provided in this package
	ClientSecret string

	// RefreshToken is the refresh token given during a previous validation
	RefreshToken string
}

// ValidationResponse is based off of https://developer.apple.com/documentation/signinwithapplerestapi/tokenresponse
type ValidationResponse struct {
	// (Reserved for future use) A token used to access allowed data. Currently, no data set has been defined for access.
	AccessToken string `json:"access_token"`

	// The type of access token. It will always be "bearer".
	TokenType string `json:"token_type"`

	// The amount of time, in seconds, before the access token expires. You can revalidate with the "RefreshToken"
	ExpiresIn int `json:"expires_in"`

	// The refresh token used to regenerate new access tokens. Store this token securely on your server.
	// The refresh token isn’t returned when validating an existing refresh token. Please refer to RefreshReponse below
	RefreshToken string `json:"refresh_token"`

	// A JSON Web Token that contains the user’s identity information.
	IDToken string `json:"id_token"`

	// Used to capture any error returned by the endpoint. Do not trust the response if this error is not nil
	Error string `json:"error"`

	// A more detailed precision about the current error.
	ErrorDescription string `json:"error_description"`
}

// RefreshResponse is a subset of ValidationResponse returned by Apple
type RefreshResponse struct {
	// (Reserved for future use) A token used to access allowed data. Currently, no data set has been defined for access.
	AccessToken string `json:"access_token"`

	// The type of access token. It will always be "bearer".
	TokenType string `json:"token_type"`

	// The amount of time, in seconds, before the access token expires. You can revalidate with this token
	ExpiresIn int `json:"expires_in"`

	// Used to capture any error returned by the endpoint. Do not trust the response if this error is not nil
	Error string `json:"error"`

	// A more detailed precision about the current error.
	ErrorDescription string `json:"error_description"`
}

// RevokeResponse is based of https://developer.apple.com/documentation/sign_in_with_apple/revoke_tokens
type RevokeResponse struct {
	// Used to capture any error returned by the endpoint
	Error string `json:"error"`

	// A more detailed precision about the current error.
	ErrorDescription string `json:"error_description"`
}
